/* Painel de Debug de Autenticação */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.panel {
  background-color: #1a1e24;
  border-radius: 1rem;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 2px solid #1DB954;
  position: relative;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(29, 185, 84, 0.2);
  background: linear-gradient(135deg, #1a1e24, #23272f);
}

.header h2 {
  color: #1DB954;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(255, 68, 68, 0.2);
  color: #ff4444;
}

/* Content */
.content {
  padding: 1.5rem;
  color: #e0e0e0;
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  color: #1DB954;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(29, 185, 84, 0.3);
  padding-bottom: 0.5rem;
}

/* Status Grid */
.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statusItem {
  background: #23272f;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statusItem.success {
  border-left-color: #22c55e;
}

.statusItem.error {
  border-left-color: #ef4444;
}

.statusItem.warning {
  border-left-color: #f59e0b;
}

.statusItem span {
  color: #b0b0b0;
}

.statusItem strong {
  color: #fff;
  font-weight: 600;
}

/* Actions */
.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.button {
  background: linear-gradient(135deg, #1DB954, #1ed760);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.button:hover:not(:disabled) {
  background: linear-gradient(135deg, #1ed760, #1DB954);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Diagnostic Result */
.diagnosticResult {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.resultItem {
  background: #23272f;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.resultItem h4 {
  color: #1DB954;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.resultItem ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.resultItem li {
  padding: 0.25rem 0;
  color: #e0e0e0;
  font-size: 0.9rem;
}

/* Logs */
.logs {
  max-height: 400px;
  overflow-y: auto;
  background: #23272f;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.noLogs {
  text-align: center;
  color: #b0b0b0;
  font-style: italic;
  margin: 0;
}

.logItem {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.logItem.info {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
}

.logItem.success {
  background: rgba(34, 197, 94, 0.1);
  border-left-color: #22c55e;
}

.logItem.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.logItem.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

.logHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.logType {
  font-weight: 600;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.1);
}

.logTime {
  font-size: 0.8rem;
  color: #b0b0b0;
}

.logMessage {
  color: #e0e0e0;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.logData {
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
}

.logData pre {
  margin: 0;
  font-size: 0.8rem;
  color: #b0b0b0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Scrollbar customizada */
.panel::-webkit-scrollbar,
.logs::-webkit-scrollbar {
  width: 8px;
}

.panel::-webkit-scrollbar-track,
.logs::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.panel::-webkit-scrollbar-thumb,
.logs::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.panel::-webkit-scrollbar-thumb:hover,
.logs::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding: 0.5rem;
  }

  .panel {
    max-height: 95vh;
  }

  .header {
    padding: 1rem;
  }

  .content {
    padding: 1rem;
  }

  .statusGrid {
    grid-template-columns: 1fr;
  }

  .diagnosticResult {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
  }

  .button {
    width: 100%;
    text-align: center;
  }
}
