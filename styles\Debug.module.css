/* Estilos para o painel de debug */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.modal {
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #333;
  background: #222;
}

.header h2 {
  margin: 0;
  color: #fff;
  font-size: 1.5rem;
}

.closeButton {
  background: none;
  border: none;
  color: #999;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
}

.closeButton:hover {
  background: #333;
  color: #fff;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  color: #fff;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #1DB954;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #f44336;
  border-radius: 8px;
  padding: 15px;
  color: #f44336;
}

.section {
  margin-bottom: 25px;
  padding: 15px;
  background: #222;
  border-radius: 8px;
  border: 1px solid #333;
}

.section h3 {
  margin: 0 0 15px 0;
  color: #1DB954;
  font-size: 1.2rem;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.infoGrid div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #2a2a2a;
  border-radius: 4px;
}

.envVars {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.envVar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #2a2a2a;
  border-radius: 4px;
  border-left: 3px solid #333;
}

.validation {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.validation div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #2a2a2a;
  border-radius: 4px;
}

.kvTest {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.kvTest div {
  padding: 10px;
  background: #2a2a2a;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.errorDetails {
  background: rgba(244, 67, 54, 0.1) !important;
  border: 1px solid #f44336;
  color: #f44336;
  font-family: monospace;
  font-size: 0.9rem;
  flex-direction: column !important;
  align-items: flex-start !important;
}

.recommendations {
  margin: 0;
  padding-left: 20px;
  color: #ffc107;
}

.recommendations li {
  margin-bottom: 8px;
  line-height: 1.4;
}

/* Status colors */
.success {
  color: #4caf50 !important;
  font-weight: bold;
}

.error {
  color: #f44336 !important;
  font-weight: bold;
}

.warning {
  color: #ffc107 !important;
  font-weight: bold;
}

.dev {
  color: #2196f3 !important;
  font-weight: bold;
}

.prod {
  color: #ff9800 !important;
  font-weight: bold;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid #333;
  background: #222;
}

.refreshButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.refreshButton:hover:not(:disabled) {
  background: #1ed760;
  transform: translateY(-1px);
}

.refreshButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .footer {
    flex-direction: column;
    gap: 10px;
  }
  
  .envVar,
  .validation div,
  .kvTest div {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
