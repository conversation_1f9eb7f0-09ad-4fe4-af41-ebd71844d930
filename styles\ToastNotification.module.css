.toastContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

.toast {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 300px;
  max-width: 400px;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease-out;
}

.toast:hover {
  transform: translateX(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.icon {
  font-size: 18px;
  flex-shrink: 0;
}

.message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  color: inherit;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.closeButton:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* Tipos de toast */
.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

/* Animações */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .toastContainer {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .toast {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .icon {
    font-size: 16px;
  }
  
  .message {
    font-size: 13px;
  }
}
