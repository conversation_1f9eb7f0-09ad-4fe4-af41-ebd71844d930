/* Modal de ativação de benefícios */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  overflow-y: auto;
}

.modal {
  background-color: #23272f;
  border-radius: 1rem;
  max-width: 500px;
  width: 90%;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 2px solid #1DB954;
  position: relative;
  animation: slideIn 0.3s ease;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(29, 185, 84, 0.2);
}

.modalHeader h2 {
  color: #1DB954;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1;
  padding: 0;
  text-align: center;
}

.closeButton:hover {
  color: #ff4444;
  background-color: rgba(255, 68, 68, 0.2);
  border-color: rgba(255, 68, 68, 0.5);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.modalContent {
  padding: 1.5rem;
  color: #e0e0e0;
}

.description {
  margin-bottom: 2rem;
  text-align: center;
}

.description p {
  color: #ccc;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

.codeInput {
  margin-bottom: 1.5rem;
}

.codeInput label {
  display: block;
  color: #fff;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.input {
  width: 100%;
  padding: 0.75rem;
  background-color: #181c22;
  border: 2px solid #23272f;
  border-radius: 0.7rem;
  color: #fff;
  font-size: 1.1rem;
  font-family: monospace;
  text-align: center;
  letter-spacing: 2px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input::placeholder {
  color: #666;
  letter-spacing: 1px;
}

.error {
  background: #dc2626;
  color: white;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: bold;
}

.actions {
  text-align: center;
  margin-bottom: 2rem;
}

.activateButton {
  background-color: #1DB954;
  color: #fff;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.activateButton:hover:not(:disabled) {
  background-color: #1ed760;
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.activateButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.help {
  background: #1a1e24;
  padding: 1rem;
  border-radius: 0.7rem;
  border-left: 4px solid #1DB954;
}

.help h4 {
  color: #fff;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.help ul {
  color: #ccc;
  margin: 0;
  padding-left: 1.5rem;
}

.help li {
  margin: 0.5rem 0;
  line-height: 1.4;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modalHeader {
    padding: 1.5rem;
  }

  .modalHeader h2 {
    font-size: 1.5rem;
  }

  .modalContent {
    padding: 1.5rem;
  }

  .input {
    font-size: 1rem;
    letter-spacing: 1px;
  }

  .activateButton {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .modalHeader {
    padding: 1rem;
  }

  .modalContent {
    padding: 1rem;
  }

  .help {
    padding: 1rem;
  }

  .input {
    font-size: 0.9rem;
    padding: 0.8rem;
  }
}
