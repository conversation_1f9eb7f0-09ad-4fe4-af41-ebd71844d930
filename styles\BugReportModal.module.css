/* Modal de Relatório de Bug */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal {
  background-color: #23272f;
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: none;
  position: relative;
  animation: slideIn 0.3s ease;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(29, 185, 84, 0.2);
}

.title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title h2 {
  color: #1DB954;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.icon {
  color: #1DB954;
  font-size: 1.25rem;
}

.closeButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1;
  padding: 0;
  text-align: center;
}

.closeButton:hover {
  color: #ff4444;
  background-color: rgba(255, 68, 68, 0.2);
  border-color: rgba(255, 68, 68, 0.5);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

/* Content */
.content {
  padding: 1.5rem;
  color: #e0e0e0;
}

.description {
  margin-bottom: 1.5rem;
  color: #b0b0b0;
  line-height: 1.5;
}

/* Form */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field label {
  color: #1DB954;
  font-weight: 600;
  font-size: 0.9rem;
}

.input,
.select,
.textarea {
  background-color: #181c22;
  border: 2px solid #23272f;
  border-radius: 0.7rem;
  color: #fff;
  padding: 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
  font-family: inherit;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.select {
  cursor: pointer;
}

/* Song Info */
.songInfo {
  background: #1a1e24;
  padding: 1rem;
  border-radius: 0.7rem;
  border-left: 4px solid #1DB954;
}

.songInfo h4 {
  margin: 0 0 0.5rem 0;
  color: #1DB954;
  font-size: 0.9rem;
}

.songInfo p {
  margin: 0;
  color: #e0e0e0;
  font-size: 0.9rem;
}

/* Status */
.status {
  padding: 0.75rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-align: center;
}

.status.success {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status.error {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Actions */
.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.cancelButton,
.submitButton {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancelButton {
  background: transparent;
  color: #b0b0b0;
  border: 2px solid #404040;
}

.cancelButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.submitButton {
  background-color: #1DB954;
  color: #fff;
}

.submitButton:hover:not(:disabled) {
  background-color: #1ed760;
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding: 0.5rem;
  }

  .modal {
    max-height: calc(100vh - 60px);
  }

  .header {
    padding: 1rem 1rem 0.75rem 1rem;
  }

  .title h2 {
    font-size: 1.25rem;
  }

  .content {
    padding: 1rem;
  }

  .closeButton {
    top: 1rem;
    right: 1rem;
    width: 2rem;
    height: 2rem;
    font-size: 1.25rem;
  }

  .actions {
    flex-direction: column;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    justify-content: center;
  }
}
