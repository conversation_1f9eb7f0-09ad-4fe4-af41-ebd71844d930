# Vercel KV Configuration (Required for production)
# These variables are automatically set by <PERSON>ercel when you add KV to your project
# For local development, you can leave these empty - the app will use in-memory storage

# Vercel KV REST API URL
KV_REST_API_URL=

# Vercel KV REST API Token
KV_REST_API_TOKEN=

# Vercel KV REST API Read Only Token (optional)
KV_REST_API_READ_ONLY_TOKEN=

# Environment
NODE_ENV=development

# Instructions:
# 1. Copy this file to .env.local for local development
# 2. For production on Vercel, these variables are set automatically when you add KV
# 3. For local development, you can leave KV variables empty - the app will work with in-memory storage
# 4. To test with real KV locally, get the values from your Vercel dashboard > Storage > KV > Settings

# Configurações para envio de emails (relatórios de erro)
# Escolha UMA das opções abaixo:

# Opção 1: SendGrid (Recomendado)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Opção 2: Gmail (Alternativa)
GMAIL_USER=<EMAIL>
GMAIL_PASS=your_app_password_here

# Opção 3: Vercel Email (Se disponível)
VERCEL_EMAIL_API_KEY=your_vercel_email_key_here

# Discord Webhook para relatórios de bug (opcional)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url_here

# Resend API para emails (recomendado)
RESEND_API_KEY=your_resend_api_key_here
