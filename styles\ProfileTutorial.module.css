/* Overlay do tutorial - UPDATED v2.0 */
.tutorialOverlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.9) !important;
  display: flex !important;
  align-items: flex-start !important;
  justify-content: center !important;
  z-index: 10001 !important;
  padding: 20px !important;
  overflow-y: auto !important;
  /* Garantir posicionamento correto */
  transform: none !important;
  margin: 0 !important;
}

/* Modal do tutorial */
.tutorialModal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
  border-radius: 20px !important;
  max-width: 600px !important;
  width: 100% !important;
  max-height: calc(100vh - 120px) !important;
  overflow-y: auto !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8) !important;
  border: 2px solid #1db954 !important;
  position: relative !important;
  animation: slideIn 0.3s ease-out !important;
  /* Posicionamento no topo - FORÇADO */
  margin: 60px auto 60px auto !important;
  flex-shrink: 0 !important;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.tutorialModal::-webkit-scrollbar {
  width: 8px;
}

.tutorialModal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.tutorialModal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.tutorialModal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Botão de fechar */
.closeButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 1.2rem;
  z-index: 1;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Header do tutorial */
.tutorialHeader {
  padding: 30px 30px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(29, 185, 84, 0.3);
}

.tutorialHeader h2 {
  color: #1db954;
  margin: 0 0 10px;
  font-size: 1.5rem;
  font-weight: 700;
}

.stepIndicator {
  color: #b3b3b3;
  font-size: 0.9rem;
  background: rgba(29, 185, 84, 0.2);
  padding: 5px 15px;
  border-radius: 20px;
  display: inline-block;
}

/* Corpo do tutorial */
.tutorialBody {
  padding: 30px;
  color: white;
  line-height: 1.6;
  max-height: 450px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4ade80 rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para o corpo do tutorial */
.tutorialBody::-webkit-scrollbar {
  width: 6px;
}

.tutorialBody::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.tutorialBody::-webkit-scrollbar-thumb {
  background: #4ade80;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.tutorialBody::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}

.stepContent {
  text-align: center;
}

.stepContent p {
  margin-bottom: 15px;
  font-size: 1rem;
}

.stepContent ul {
  text-align: left;
  margin: 15px 0;
  padding-left: 20px;
}

.stepContent li {
  margin-bottom: 8px;
  color: #e0e0e0;
}

/* Ícones especiais */
.welcomeIcon,
.finalIcon,
.backupIcon {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Exemplo de XP */
.xpExample {
  background: rgba(29, 185, 84, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin: 20px 0;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

.levelBadge {
  background: linear-gradient(45deg, #1db954, #1ed760);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 10px;
}

.xpBar {
  background: rgba(255, 255, 255, 0.2);
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
  margin: 10px 0;
}

.xpProgress {
  background: linear-gradient(90deg, #1db954, #1ed760);
  height: 100%;
  border-radius: 5px;
  transition: width 0.3s ease;
}

.xpText {
  color: #1db954;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Exemplo de conquista */
.achievementExample {
  margin: 20px 0;
}

.achievement {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

.achievementIcon {
  font-size: 2rem;
  background: linear-gradient(45deg, #1db954, #1ed760);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.achievementTitle {
  color: white;
  font-weight: 600;
  margin-bottom: 5px;
}

.achievementDesc {
  color: #b3b3b3;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.achievementProgress {
  color: #1db954;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Exemplo de abas */
.tabsExample {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
}

.tabExample {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  text-align: left;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.tabExample svg {
  color: #1db954;
  font-size: 1.2rem;
}

.tabExample span {
  color: white;
  font-weight: 600;
  margin-right: 10px;
}

.tabExample p {
  color: #b3b3b3;
  font-size: 0.9rem;
  margin: 0;
  flex: 1;
}

/* Exemplo de notificação */
.notificationExample {
  margin: 20px 0;
}

.mockNotification {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%);
  border: 2px solid #1db954;
  border-radius: 15px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notifIcon {
  font-size: 2rem;
  background: linear-gradient(45deg, #1db954, #1ed760);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifTitle {
  color: #1db954;
  font-weight: 600;
  margin-bottom: 5px;
}

.notifDesc {
  color: white;
  font-weight: 600;
  margin-bottom: 5px;
}

.notifXp {
  color: #1ed760;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Exemplo de backup */
.backupExample {
  background: rgba(29, 185, 84, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin: 20px 0;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

/* Caixa de dica */
.tipBox {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  padding: 15px;
  border-radius: 10px;
  margin-top: 20px;
  color: #ffc107;
  font-size: 0.9rem;
}

/* Footer do tutorial */
.tutorialFooter {
  padding: 20px 30px 30px;
  border-top: 1px solid rgba(29, 185, 84, 0.3);
}

.progressBar {
  background: rgba(255, 255, 255, 0.2);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 20px;
}

.progressFill {
  background: linear-gradient(90deg, #1db954, #1ed760);
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.navigationButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.navButton,
.finishButton {
  background: #1db954;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.navButton:hover,
.finishButton:hover {
  background: #1ed760;
  transform: translateY(-2px);
}

.navButton:disabled {
  background: #555;
  cursor: not-allowed;
  transform: none;
}

.finishButton {
  background: linear-gradient(45deg, #1db954, #1ed760);
  font-size: 1rem;
  padding: 15px 25px;
}

/* Responsividade */
@media (max-width: 768px) {
  .tutorialModal {
    margin: 40px 10px 40px 10px !important;
    max-height: calc(100vh - 80px) !important;
  }

  .tutorialHeader,
  .tutorialBody,
  .tutorialFooter {
    padding: 20px;
  }

  .tabsExample {
    gap: 10px;
  }

  .tabExample {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }

  .navigationButtons {
    flex-direction: column;
  }

  .navButton,
  .finishButton {
    width: 100%;
    justify-content: center;
  }
}
