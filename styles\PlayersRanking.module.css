/* Overlay do modal */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  backdrop-filter: blur(5px);
}

/* Modal principal */
.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 100%);
  position: relative;
}

.header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
}

.closeButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  z-index: 10;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Controles */
.controls {
  display: flex;
  gap: 20px;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  align-items: center;
  flex-wrap: wrap;
}

.searchContainer {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.searchIcon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #888888;
  font-size: 16px;
}

.searchInput {
  width: 100%;
  padding: 12px 15px 12px 45px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  color: #ffffff;
  font-size: 14px;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.searchInput::placeholder {
  color: #888888;
}

.sortContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #cccccc;
  font-size: 14px;
}

.sortSelect {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sortSelect:focus {
  outline: none;
  border-color: #4CAF50;
}

.sortSelect option {
  background: #2a2a2a;
  color: #ffffff;
  padding: 8px;
}

.sortSelect option:hover {
  background: #3a3a3a;
}

/* Conteúdo */
.content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

/* Estilização da scrollbar */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Para Firefox */
.content {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) rgba(255, 255, 255, 0.05);
}

/* Lista de jogadores */
.playersList {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
}

.playerItem {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.playerItem:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(5px);
}

.playerItem.currentUser {
  background: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4CAF50;
}

.playerItem.currentUser:hover {
  background: rgba(76, 175, 80, 0.15);
}

/* Ranking */
.rankContainer {
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  flex-shrink: 0;
}

.rankNumber {
  color: #888888;
  font-size: 16px;
}

/* Info do jogador */
.playerInfo {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
  min-width: 0;
}

.playerDetails {
  flex: 1;
  min-width: 0;
}

.playerName {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.youBadge {
  background: #4CAF50;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.playerUsername {
  color: #888888;
  font-size: 12px;
  margin-bottom: 2px;
}

.playerBio {
  color: #cccccc;
  font-size: 11px;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

/* Nível */
.levelContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.levelBadge {
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 70px;
}

.xpText {
  color: #888888;
  font-size: 10px;
  text-align: center;
}

/* Estatísticas */
.statsContainer {
  display: flex;
  gap: 15px;
  flex-shrink: 0;
  min-width: 0;
  overflow: hidden;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 40px;
}

.statIcon {
  color: #4CAF50;
  font-size: 14px;
}

.statItem span {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
}

/* Estados de loading/error */
.loading,
.error,
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p,
.error p,
.empty p {
  color: #ffffff;
  margin: 0;
  font-size: 16px;
}

.retryButton {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 15px;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  padding: 15px 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  text-align: center;
}

.footer p {
  color: #888888;
  margin: 0;
  font-size: 12px;
}

.footer p:first-child {
  margin-bottom: 5px;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal {
    margin: 10px;
    max-height: 95vh;
  }

  .header,
  .controls {
    padding: 15px 20px;
  }

  .controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .searchContainer {
    min-width: auto;
  }

  .playerItem {
    padding: 12px 20px;
    gap: 15px;
  }

  .statsContainer {
    gap: 10px;
  }

  .playerBio {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .playerItem {
    flex-wrap: wrap;
    gap: 10px;
  }

  .statsContainer {
    width: 100%;
    justify-content: space-around;
    margin-top: 10px;
  }

  .levelContainer {
    margin-top: 5px;
  }
}
