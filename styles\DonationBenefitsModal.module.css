/* Modal de benefícios <PERSON> */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.8);
  border: 2px solid #ff6b6b;
  animation: slideIn 0.3s ease-out;
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #333;
}

.modalHeader h2 {
  color: #fff;
  margin: 0;
  font-size: 1.8rem;
  background: linear-gradient(45deg, #ff6b6b, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.closeButton {
  background: none;
  border: none;
  color: #999;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: #333;
  color: #fff;
}

.modalContent {
  padding: 2rem;
}

.thankYou {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  border-radius: 15px;
  color: white;
}

.thankYou h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
}

.thankYou p {
  margin: 0;
  opacity: 0.9;
}

.benefitsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.benefitItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #2a2a2a;
  border-radius: 12px;
  border-left: 4px solid #ff6b6b;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.benefitItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.benefitItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.benefitItem:hover::before {
  opacity: 1;
}

.benefitIcon {
  font-size: 2rem;
  min-width: 3rem;
  text-align: center;
}

.benefitInfo {
  flex: 1;
  color: #fff;
}

.benefitInfo h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: bold;
}

.benefitInfo p {
  margin: 0;
  color: #ccc;
  font-size: 0.9rem;
  line-height: 1.4;
}

.benefitType {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.footer {
  text-align: center;
  padding: 1.5rem;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
}

.footer p {
  color: #ccc;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.footer p:first-child {
  color: #fff;
  font-weight: bold;
  font-size: 1rem;
}

.profileButton {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.profileButton:hover {
  background: linear-gradient(45deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

/* Custom scrollbar styling for donation modal */
.modal::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

/* Responsividade */
@media (max-width: 768px) {
  .modal {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modalHeader {
    padding: 1.5rem;
  }

  .modalHeader h2 {
    font-size: 1.5rem;
  }

  .modalContent {
    padding: 1.5rem;
  }

  .benefitItem {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .benefitIcon {
    font-size: 2.5rem;
  }

  .benefitType {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .modalHeader {
    padding: 1rem;
  }

  .modalContent {
    padding: 1rem;
  }

  .thankYou {
    padding: 1rem;
  }

  .thankYou h3 {
    font-size: 1.2rem;
  }

  .benefitItem {
    padding: 1rem;
  }
}
