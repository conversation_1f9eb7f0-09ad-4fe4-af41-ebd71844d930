/* Overlay do seletor de avatar */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10001;
  padding: 20px;
  overflow-y: auto;
}

/* Modal do seletor */
.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
  border: 2px solid #1db954;
  margin: 60px auto 60px auto;
  animation: slideIn 0.3s ease-out;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(29, 185, 84, 0.3);
}

.header h3 {
  color: #1db954;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.closeButton {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 1.2rem;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Conteúdo */
.content {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.content h4 {
  color: white;
  margin: 0 0 15px;
  font-size: 1rem;
  font-weight: 600;
}

/* Avatar atual */
.currentSection {
  text-align: center;
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.currentAvatar {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(29, 185, 84, 0.2);
  border: 3px solid #1db954;
  overflow: hidden;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarEmoji {
  font-size: 2.5rem;
}

.defaultIcon {
  font-size: 2rem;
  color: #1db954;
}

/* Upload section */
.uploadSection {
  margin-bottom: 25px;
  text-align: center;
}

.uploadButton {
  background: #1db954;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  text-decoration: none;
}

.uploadButton:hover {
  background: #1ed760;
  transform: translateY(-2px);
}

.uploadInfo {
  color: #888;
  font-size: 0.8rem;
  margin-top: 8px;
  margin-bottom: 0;
}

/* Avatares predefinidos */
.predefinedSection {
  margin-bottom: 25px;
}

.avatarGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.avatarOption {
  width: 50px;
  height: 50px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.2s;
}

.avatarOption:hover {
  background: rgba(29, 185, 84, 0.2);
  border-color: rgba(29, 185, 84, 0.5);
  transform: scale(1.1);
}

.avatarOption.selected {
  background: rgba(29, 185, 84, 0.3);
  border-color: #1db954;
  transform: scale(1.1);
}

/* Opção sem avatar */
.noAvatarSection {
  text-align: center;
}

.noAvatarOption {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 10px;
  padding: 15px 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  color: #b3b3b3;
  font-weight: 600;
  transition: all 0.2s;
}

.noAvatarOption:hover {
  background: rgba(29, 185, 84, 0.2);
  border-color: rgba(29, 185, 84, 0.5);
  color: white;
}

.noAvatarOption.selected {
  background: rgba(29, 185, 84, 0.3);
  border-color: #1db954;
  color: white;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  padding: 20px 25px;
  border-top: 1px solid rgba(29, 185, 84, 0.3);
}

.cancelButton,
.saveButton {
  flex: 1;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  border: none;
}

.cancelButton {
  background: transparent;
  color: #888;
  border: 1px solid #888;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.saveButton {
  background: #1db954;
  color: white;
}

.saveButton:hover {
  background: #1ed760;
  transform: translateY(-2px);
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding: 10px;
  }

  .modal {
    max-width: 100%;
    max-height: calc(100vh - 80px);
    margin: 40px auto 40px auto;
  }

  .content {
    padding: 20px;
  }

  .avatarGrid {
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
    gap: 8px;
  }

  .avatarOption {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .currentAvatar {
    width: 70px;
    height: 70px;
  }

  .avatarEmoji {
    font-size: 2rem;
  }

  .footer {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .modal {
    max-height: calc(100vh - 40px);
    margin: 20px auto 20px auto;
    border-radius: 15px;
  }

  .content {
    padding: 15px;
  }

  .avatarGrid {
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 6px;
  }

  .avatarOption {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .currentAvatar {
    width: 60px;
    height: 60px;
  }

  .avatarEmoji {
    font-size: 1.8rem;
  }
}
