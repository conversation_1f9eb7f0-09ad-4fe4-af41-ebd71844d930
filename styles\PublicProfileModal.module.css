/* Estilos para o modal de perfil público - Estilo Steam */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #1b2838 0%, #2a475e 100%);
  border-radius: 12px;
  padding: 0;
  width: 100%;
  max-width: 900px;
  max-height: calc(100vh - 40px);
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.7);
  border: 2px solid #66c0f4;
  position: relative;
  flex-shrink: 0;
}

/* Header com background gradient estilo Steam */
.header {
  position: relative;
  background: linear-gradient(135deg, #171a21 0%, #1b2838 50%, #2a475e 100%);
  padding: 20px 30px;
  border-bottom: 2px solid #66c0f4;
  overflow: hidden;
}

.headerBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.headerContent {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* Removido padding-right para evitar conflito com botão absoluto */
}

.title {
  color: #66c0f4;
  font-size: 28px;
  font-weight: bold;
  margin: 0;
  margin-right: 60px; /* Espaço fixo para o botão X */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  flex: 1; /* Permite que o título ocupe o espaço disponível */
  min-width: 0; /* Permite que o texto seja truncado se necessário */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.headerStats {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0; /* Evita que as estatísticas sejam comprimidas */
}

.headerLevel {
  font-size: 16px;
  font-weight: bold;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  border: 1px solid currentColor;
}

.closeButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #66c0f4;
  color: #66c0f4;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  z-index: 10; /* Z-index mais alto para garantir que fique sempre no topo */
  flex-shrink: 0; /* Evita que o botão seja comprimido */
}

.closeButton:hover {
  background: #66c0f4;
  color: #1b2838;
  transform: scale(1.1);
}

/* Conteúdo principal */
.content {
  padding: 0;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
  background: linear-gradient(135deg, #1b2838 0%, #2a475e 100%);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #66c0f4;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(102, 192, 244, 0.3);
  border-top: 3px solid #66c0f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #ff6b6b;
  text-align: center;
}

.retryButton {
  background: linear-gradient(135deg, #66c0f4, #4a9eff);
  color: #1b2838;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.retryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 192, 244, 0.3);
}

/* Conteúdo do perfil */
.profileContent {
  padding: 30px;
  color: #c7d5e0;
}

/* Cabeçalho do perfil estilo Steam */
.profileHeader {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(23, 26, 33, 0.8), rgba(27, 40, 56, 0.8));
  border-radius: 12px;
  border: 1px solid rgba(102, 192, 244, 0.2);
  position: relative;
  overflow: hidden;
}

.profileHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%2366c0f4" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.avatarContainer {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.avatarFrame {
  position: relative;
  padding: 4px;
  background: linear-gradient(45deg, #66c0f4, #4a9eff, #66c0f4);
  border-radius: 50%;
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% { box-shadow: 0 0 20px rgba(102, 192, 244, 0.5); }
  100% { box-shadow: 0 0 30px rgba(102, 192, 244, 0.8); }
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2a475e, #1b2838);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 3px solid #1b2838;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarEmoji {
  font-size: 48px;
  color: #66c0f4;
}

.levelBadge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  border: 3px solid #1b2838;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* Status online */
.onlineStatus {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.statusIndicator.online {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid #4caf50;
  color: #4caf50;
}

.statusIndicator.offline {
  background: rgba(158, 158, 158, 0.2);
  border: 1px solid #9e9e9e;
  color: #9e9e9e;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.statusText {
  font-size: 13px;
}

/* Informações do perfil */
.profileInfo {
  flex: 1;
  position: relative;
  z-index: 2;
}

.nameSection {
  margin-bottom: 15px;
}

.displayName {
  color: #66c0f4;
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 5px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.username {
  color: #8f98a0;
  font-size: 16px;
  margin: 0;
  font-weight: 400;
}

.titleBadge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1b2838;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.titleIcon {
  font-size: 16px;
}

.levelInfo {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
}

.levelText {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #66c0f4;
  font-weight: bold;
  font-size: 18px;
}

.levelIcon {
  color: #ffd700;
}

.xpText {
  color: #8f98a0;
  font-size: 16px;
}

.bio {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #66c0f4;
  margin-top: 15px;
}

.bio p {
  margin: 0;
  font-style: italic;
  color: #c7d5e0;
  line-height: 1.5;
}

/* Ações do perfil */
.profileActions {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.addFriendButton {
  background: linear-gradient(135deg, #66c0f4, #4a9eff);
  color: #1b2838;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 192, 244, 0.3);
}

.addFriendButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 192, 244, 0.4);
}

.friendStatus {
  display: flex;
  justify-content: center;
}

.friendBadge {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.friendIcon {
  font-size: 16px;
}

.loginPrompt {
  text-align: center;
  color: #8f98a0;
  font-style: italic;
}

/* Seções principais */
.sectionTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #66c0f4;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(102, 192, 244, 0.3);
}

.sectionIcon {
  font-size: 28px;
  color: #ffd700;
}

/* Estatísticas estilo Steam */
.statsShowcase {
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(23, 26, 33, 0.6), rgba(27, 40, 56, 0.6));
  border-radius: 12px;
  border: 1px solid rgba(102, 192, 244, 0.2);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.statCard {
  background: linear-gradient(135deg, rgba(42, 71, 94, 0.8), rgba(27, 40, 56, 0.8));
  border: 1px solid rgba(102, 192, 244, 0.3);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 192, 244, 0.2);
  border-color: #66c0f4;
}

.statIcon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #66c0f4, #4a9eff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1b2838;
  font-size: 20px;
  flex-shrink: 0;
}

.statContent {
  flex: 1;
}

.statValue {
  font-size: 28px;
  font-weight: bold;
  color: #66c0f4;
  line-height: 1;
  margin-bottom: 5px;
}

.statLabel {
  font-size: 14px;
  color: #8f98a0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Seção de Conquistas */
.achievementsShowcase {
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(23, 26, 33, 0.6), rgba(27, 40, 56, 0.6));
  border-radius: 12px;
  border: 1px solid rgba(102, 192, 244, 0.2);
}

.achievementsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.achievementCard {
  background: linear-gradient(135deg, rgba(42, 71, 94, 0.8), rgba(27, 40, 56, 0.8));
  border: 1px solid rgba(102, 192, 244, 0.3);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.achievementCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 192, 244, 0.2);
  border-color: #66c0f4;
}

.achievementCard.common {
  border-left: 4px solid #9e9e9e;
}

.achievementCard.uncommon {
  border-left: 4px solid #4caf50;
}

.achievementCard.rare {
  border-left: 4px solid #2196f3;
}

.achievementCard.epic {
  border-left: 4px solid #9c27b0;
}

.achievementCard.legendary {
  border-left: 4px solid #ff9800;
}

.achievementIcon {
  font-size: 32px;
  flex-shrink: 0;
}

.achievementInfo {
  flex: 1;
}

.achievementTitle {
  font-weight: bold;
  color: #66c0f4;
  margin-bottom: 5px;
  font-size: 16px;
}

.achievementDescription {
  color: #8f98a0;
  font-size: 14px;
  margin-bottom: 5px;
  line-height: 1.3;
}

.achievementDate {
  color: #66c0f4;
  font-size: 12px;
  opacity: 0.8;
}

.achievementRarity {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  flex-shrink: 0;
}

.rarityIcon {
  font-size: 16px;
  color: #ffd700;
}

.moreAchievements {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  background: rgba(102, 192, 244, 0.1);
  border: 2px dashed rgba(102, 192, 244, 0.3);
  border-radius: 8px;
  color: #66c0f4;
  font-weight: bold;
}

/* Seção de Badges */
.badgesShowcase {
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(23, 26, 33, 0.6), rgba(27, 40, 56, 0.6));
  border-radius: 12px;
  border: 1px solid rgba(102, 192, 244, 0.2);
}

.badgesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.badgeCard {
  background: linear-gradient(135deg, rgba(42, 71, 94, 0.8), rgba(27, 40, 56, 0.8));
  border: 1px solid rgba(102, 192, 244, 0.3);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.badgeCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 192, 244, 0.2);
  border-color: #66c0f4;
}

.badgeCard.common {
  border-top: 3px solid #9e9e9e;
}

.badgeCard.uncommon {
  border-top: 3px solid #4caf50;
}

.badgeCard.rare {
  border-top: 3px solid #2196f3;
}

.badgeCard.epic {
  border-top: 3px solid #9c27b0;
}

.badgeCard.legendary {
  border-top: 3px solid #ff9800;
}

.badgeIcon {
  font-size: 36px;
  margin-bottom: 5px;
}

.badgeTitle {
  font-weight: bold;
  color: #66c0f4;
  font-size: 14px;
  line-height: 1.2;
}

.moreBadges {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  background: rgba(102, 192, 244, 0.1);
  border: 2px dashed rgba(102, 192, 244, 0.3);
  border-radius: 8px;
  color: #66c0f4;
  font-weight: bold;
}

/* Informações adicionais */
.additionalInfo {
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(23, 26, 33, 0.4), rgba(27, 40, 56, 0.4));
  border-radius: 8px;
  border: 1px solid rgba(102, 192, 244, 0.1);
}

.memberInfo {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.infoItem {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #8f98a0;
  font-size: 14px;
}

.infoIcon {
  color: #66c0f4;
  font-size: 16px;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal {
    max-width: 95vw;
    margin: 10px;
  }

  .closeButton {
    top: 12px;
    right: 12px;
    width: 38px;
    height: 38px;
    font-size: 15px;
  }

  .title {
    font-size: 22px;
    margin-right: 55px; /* Ajuste para tablets */
  }

  .profileHeader {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .displayName {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 15px 20px;
  }

  .closeButton {
    top: 10px;
    right: 10px;
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .title {
    font-size: 20px;
    margin-right: 50px; /* Menos espaço em telas pequenas */
  }

  .profileContent {
    padding: 20px;
  }

  .avatar {
    width: 80px;
    height: 80px;
  }

  .avatarEmoji {
    font-size: 32px;
  }

  .levelBadge {
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
}
