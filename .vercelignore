


# Builds locais
/.next/
/hostinger-build/
/out/

# Node modules
/node_modules/

# Logs
*.log
npm-debug.log*

# Arquivos temporários
.DS_Store
Thumbs.db

# Arquivos de desenvolvimento
.env.local
.env.development.local
.env.test.local
.env.production.local

# Documentação
README*.md
GUIA*.md
DEPLOY*.md

# Arquivos de configuração não utilizados
railway-backend/
railway.json
Dockerfile

# Scripts desnecessários para produção
scripts/
*.js.map

# Arquivos de teste
test/
tests/
__tests__/

# Outros
.git/
.gitignore

# Não incluir áudio nas funções serverless (mas manter para static)
!public/audio/
