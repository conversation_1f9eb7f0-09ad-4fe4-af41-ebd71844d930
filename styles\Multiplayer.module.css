/* Container principal */
.container {
  min-height: 100vh; /* Ocupa toda a altura da viewport */
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Overlay com blur apenas para o conteúdo */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  z-index: -1;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Lobby inicial */
.lobbyContainer {
  background: linear-gradient(145deg, rgba(35, 39, 47, 0.95), rgba(42, 52, 71, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 50px;
  max-width: 550px;
  width: 100%;
  text-align: center;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(29, 185, 84, 0.2);
  position: relative;
  overflow: hidden;
}

.lobbyContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1DB954, transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Container do título com botões */
.titleContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 35px;
  position: relative;
}

.title {
  font-size: 2.8rem;
  margin: 0;
  background: linear-gradient(135deg, #1DB954, #1ed760, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-shadow: 0 0 30px rgba(29, 185, 84, 0.3);
  position: relative;
}

.headerButtons {
  display: flex;
  gap: 10px;
}

.helpButton {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.8), rgba(68, 160, 141, 0.8));
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 12px 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.helpButton:hover {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
}

.buttonGroup {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

/* Botão de voltar estilizado */
.backButton {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.8), rgba(229, 57, 53, 0.8));
  border: none;
  border-radius: 12px;
  color: white;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 15px rgba(244, 67, 54, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(244, 67, 54, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Melhorias no título da seção */
.playersTitle {
  font-size: 1.4rem;
  margin-bottom: 20px;
  color: #1DB954;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.primaryButton {
  flex: 1;
  padding: 18px 30px;
  background: linear-gradient(135deg, #1DB954, #1ed760);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(29, 185, 84, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.primaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.primaryButton:hover::before {
  left: 100%;
}

.primaryButton:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(29, 185, 84, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.primaryButton:active {
  transform: translateY(-1px) scale(0.98);
}

.primaryButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondaryButton {
  flex: 1;
  padding: 18px 30px;
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(78, 205, 196, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.secondaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.secondaryButton:hover::before {
  left: 100%;
}

.secondaryButton:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(78, 205, 196, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.secondaryButton:active {
  transform: translateY(-1px) scale(0.98);
}

.secondaryButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Formulários */
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: left;
}

.label {
  font-weight: bold;
  color: #e0e0e0;
}

.input {
  padding: 16px 20px;
  border: 2px solid rgba(29, 185, 84, 0.3);
  border-radius: 12px;
  background: rgba(35, 39, 47, 0.8);
  color: white;
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.input:focus {
  outline: none;
  border-color: #1DB954;
  background: rgba(35, 39, 47, 0.9);
  box-shadow:
    0 8px 25px rgba(29, 185, 84, 0.2),
    0 0 0 3px rgba(29, 185, 84, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Sala de espera */
.waitingRoom {
  background: linear-gradient(145deg, rgba(35, 39, 47, 0.95), rgba(42, 52, 71, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 50px;
  max-width: 650px;
  width: 100%;
  text-align: center;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(29, 185, 84, 0.2);
  position: relative;
  overflow: hidden;
}

.waitingRoom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1DB954, transparent);
  animation: shimmer 2s ease-in-out infinite;
}

.roomCode {
  font-size: 2.2rem;
  font-weight: 700;
  color: #1DB954;
  margin: 25px 0;
  padding: 20px 25px;
  background: linear-gradient(135deg, rgba(29, 185, 84, 0.15), rgba(30, 215, 96, 0.15));
  border-radius: 16px;
  letter-spacing: 4px;
  border: 2px solid rgba(29, 185, 84, 0.3);
  box-shadow:
    0 8px 25px rgba(29, 185, 84, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  font-family: 'Courier New', monospace;
  position: relative;
  overflow: hidden;
}

.roomCode::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: codeShimmer 3s ease-in-out infinite;
}

@keyframes codeShimmer {
  0%, 100% { left: -100%; }
  50% { left: 100%; }
}

.playersList {
  margin: 30px 0;
}



.playerItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  margin: 12px 0;
  background: linear-gradient(135deg, rgba(35, 39, 47, 0.8), rgba(42, 52, 71, 0.8));
  border-radius: 12px;
  border: 1px solid rgba(29, 185, 84, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.playerItem:hover {
  transform: translateY(-2px);
  border-color: rgba(29, 185, 84, 0.4);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.playerName {
  font-weight: 600;
  font-size: 1.1rem;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hostBadge {
  background: linear-gradient(135deg, #1DB954, #1ed760);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow:
    0 4px 15px rgba(29, 185, 84, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Jogo */
.gameContainer {
  background: linear-gradient(145deg, rgba(35, 39, 47, 0.95), rgba(42, 52, 71, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  max-width: 850px;
  width: 100%;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(29, 185, 84, 0.2);
  position: relative;
  overflow: hidden;
}

.gameContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1DB954, transparent);
  animation: shimmer 2s ease-in-out infinite;
}

.gameHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.roundInfo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4ecdc4;
}

.scoreBoard {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.scoreItem {
  background: rgba(255, 255, 255, 0.08);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Player atual */
.currentPlayer {
  background: rgba(78, 205, 196, 0.3);
  border: 2px solid #4ecdc4;
}

/* Informação de pontuação */
.pointsInfo {
  text-align: center;
  margin: 15px 0 10px 0;
}

.pointsText {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.pointsExplanation {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  margin: 20px auto;
  max-width: 600px;
  font-size: 0.9rem;
}

.pointsExplanation p {
  margin: 5px 0;
  color: #ccc;
}

.pointsExplanation p:first-child {
  font-weight: bold;
  color: #4ecdc4;
}

.pointsEarned {
  background: rgba(76, 175, 80, 0.3);
  color: #4caf50;
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: bold;
  margin-top: 10px;
  display: inline-block;
  border: 1px solid rgba(76, 175, 80, 0.5);
}

/* Caixa de dicas */
.hintBox {
  background: rgba(255, 193, 7, 0.2);
  border: 2px solid #ffc107;
  color: #ffc107;
  padding: 12px 20px;
  border-radius: 10px;
  margin: 20px auto;
  max-width: 500px;
  text-align: center;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Mensagens */
.message {
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  text-align: center;
  font-weight: bold;
}

.messageSuccess {
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid #4caf50;
  color: #4caf50;
}

.messageError {
  background: rgba(244, 67, 54, 0.2);
  border: 2px solid #f44336;
  color: #f44336;
}

.messageInfo {
  background: rgba(33, 150, 243, 0.2);
  border: 2px solid #2196f3;
  color: #2196f3;
}

.messageWarning {
  background: rgba(255, 193, 7, 0.2);
  border: 2px solid #ffc107;
  color: #ffc107;
}

/* Loading */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  font-size: 1.2rem;
}

.spinner {
  width: 1.3em;
  height: 1.3em;
  border: 3px solid #1db95444;
  border-top: 3px solid #1db954;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  display: inline-block;
  vertical-align: middle;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 📱 RESPONSIVIDADE COMPLETA PARA MULTIPLAYER */
@media (max-width: 768px) {
  .content {
    padding: 15px;
  }

  .lobbyContainer {
    padding: 35px;
    max-width: 90%;
  }

  .waitingRoom {
    padding: 35px;
    max-width: 90%;
  }

  .gameContainer {
    padding: 30px;
    max-width: 95%;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 15px;
  }

  .gameHeader {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .scoreBoard {
    justify-content: center;
    gap: 10px;
  }

  .scoreItem {
    font-size: 0.8rem;
    padding: 6px 10px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  .roomCode {
    font-size: 1.5rem;
    letter-spacing: 2px;
    padding: 12px;
  }

  .roundInfo {
    font-size: 1.3rem;
  }

  .primaryButton, .secondaryButton {
    padding: 15px 25px;
    font-size: 1.1rem;
  }

  .input {
    padding: 14px 18px;
    font-size: 1rem;
  }

  .playerItem {
    padding: 8px 12px;
    margin: 6px 0;
  }

  .playerName {
    font-size: 0.9rem;
  }

  .hostBadge {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 10px;
  }

  .lobbyContainer {
    padding: 25px;
    max-width: 95%;
  }

  .waitingRoom {
    padding: 25px;
    max-width: 95%;
  }

  .gameContainer {
    padding: 20px;
    max-width: 98%;
  }

  .title {
    font-size: 1.8rem;
    margin-bottom: 15px;
  }

  .roomCode {
    font-size: 1.3rem;
    letter-spacing: 1px;
    padding: 10px;
  }

  .roundInfo {
    font-size: 1.1rem;
  }

  .scoreBoard {
    gap: 8px;
  }

  .scoreItem {
    font-size: 0.75rem;
    padding: 5px 8px;
  }

  .primaryButton, .secondaryButton {
    padding: 14px 20px;
    font-size: 1rem;
  }

  .input {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .playerItem {
    padding: 6px 10px;
    margin: 4px 0;
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }

  .playerName {
    font-size: 0.85rem;
  }

  .hostBadge {
    font-size: 0.65rem;
    padding: 2px 5px;
  }

  .pointsInfo {
    margin: 10px 0 8px 0;
  }

  .pointsText {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .hintBox {
    padding: 10px 15px;
    font-size: 0.85rem;
    margin: 15px auto;
  }

  .message {
    padding: 12px;
    margin: 15px 0;
    font-size: 0.9rem;
  }

  .pointsExplanation {
    padding: 12px;
    font-size: 0.8rem;
  }

  .pointsEarned {
    font-size: 1rem;
    padding: 6px 12px;
  }
}

/* 📱 ORIENTAÇÃO PAISAGEM EM MOBILE */
@media (max-height: 600px) and (orientation: landscape) {
  .content {
    padding: 10px;
    justify-content: flex-start;
  }

  .lobbyContainer, .waitingRoom, .gameContainer {
    margin: 10px 0;
    padding: 15px;
  }

  .title {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .gameHeader {
    margin-bottom: 15px;
  }
}

.audioPlayBtnCustom:focus {
  outline: 2px solid #1DB954;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px #1DB95455;
  transition: box-shadow 0.2s;
}

.audioPlayBtnCustom:active {
  transform: scale(0.96);
  transition: transform 0.1s;
}
