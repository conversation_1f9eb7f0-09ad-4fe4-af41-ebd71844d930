/* Estilos para páginas de administração */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Autenticação */
.authContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 40px;
  margin: 20px auto;
  max-width: 400px;
}

.authForm {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.passwordInput {
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.passwordInput:focus {
  outline: none;
  border-color: #1DB954;
}

.authButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.authButton:hover {
  background: #1ed760;
}

.authError {
  color: #dc3545;
  text-align: center;
  font-size: 14px;
  margin-top: 10px;
}

/* Header */
.header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.headerContent h1 {
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.headerActions {
  display: flex;
  gap: 10px;
}

/* Estatísticas */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.statCard {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.statCard h3 {
  color: #666;
  font-size: 14px;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statNumber {
  font-size: 28px;
  font-weight: bold;
  color: #1DB954;
  margin: 0;
}

/* Controles */
.controls {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.searchContainer {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.searchInput {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #1DB954;
}

.filtersContainer {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filterSelect,
.sortSelect {
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.filterSelect:focus,
.sortSelect:focus {
  outline: none;
  border-color: #1DB954;
}

/* Botões */
.refreshButton,
.exportButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.refreshButton:hover,
.exportButton:hover {
  background: #1ed760;
}

.exportButton {
  background: #007bff;
}

.exportButton:hover {
  background: #0056b3;
}

.retryButton {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background: #e0a800;
}

.cleanupButton {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.cleanupButton:hover {
  background: #c82333;
}

/* Lista de contas */
.accountsList {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.resultsHeader {
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  font-size: 14px;
  color: #666;
}

.accountCard {
  border-bottom: 1px solid #e1e5e9;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.accountCard:hover {
  background: #f8f9fa;
}

.accountCard:last-child {
  border-bottom: none;
}

.activeAccount {
  border-left: 4px solid #1DB954;
}

.accountHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.accountInfo h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 18px;
}

.accountUsername {
  color: #666;
  font-size: 14px;
}

.activeBadge {
  background: #1DB954;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 10px;
}

.accountActions {
  display: flex;
  gap: 8px;
}

.viewButton {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.viewButton:hover {
  background: #0056b3;
}

.deleteButton {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteButton:hover {
  background: #c82333;
}

.accountDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.detailLabel {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

/* Estados */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
  animation: spin 1s linear infinite;
  font-size: 24px;
  color: #1DB954;
  margin-bottom: 15px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error h2 {
  color: #dc3545;
  margin-bottom: 10px;
}

.noResults {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.noResults h3 {
  margin: 15px 0 5px 0;
  color: #333;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
}

.modalHeader h2 {
  margin: 0;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #333;
}

.modalBody {
  padding: 20px;
}

/* Detalhes do perfil */
.profileDetails h3 {
  color: #333;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e5e9;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.infoItem label {
  font-weight: 600;
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoItem span {
  color: #333;
  font-size: 16px;
}

/* Responsividade */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .headerContent {
    flex-direction: column;
    align-items: stretch;
  }

  .controls {
    flex-direction: column;
  }

  .filtersContainer {
    justify-content: stretch;
  }

  .filterSelect,
  .sortSelect {
    flex: 1;
  }

  .accountDetails {
    grid-template-columns: 1fr;
  }

  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .modal {
    padding: 10px;
  }

  .modalContent {
    max-height: 90vh;
  }

  .infoGrid {
    grid-template-columns: 1fr;
  }
}

/* Estilos para navegação admin */
.adminNav {
  display: flex;
  gap: 1rem;
}

.navButton {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.navButton:hover {
  background: linear-gradient(45deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  color: #e5e5e5;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

/* Estilos para página de doações */
.donationsList {
  margin-top: 2rem;
}

.donationCard {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.donationCard:hover {
  border-color: #666;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.donationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.donationHeader h3 {
  color: #10b981;
  margin: 0;
  font-size: 1.5rem;
}

.status {
  background: #fbbf24;
  color: #92400e;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.donationDetails {
  margin-bottom: 1.5rem;
}

.donationDetails p {
  margin: 0.5rem 0;
  color: #d1d5db;
}

.donationActions {
  display: flex;
  gap: 1rem;
}

.actionButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.approveButton {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
}

.approveButton:hover:not(:disabled) {
  background: linear-gradient(45deg, #059669, #047857);
  transform: translateY(-1px);
}

.rejectButton {
  background: linear-gradient(45deg, #ef4444, #dc2626);
  color: white;
}

.rejectButton:hover:not(:disabled) {
  background: linear-gradient(45deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
}

.emptyState {
  text-align: center;
  padding: 3rem;
  color: #9ca3af;
}

.emptyState h2 {
  color: #10b981;
  margin-bottom: 1rem;
}

.instructions {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.instructions h3 {
  color: #f3f4f6;
  margin-bottom: 1rem;
}

.instructions ul {
  color: #d1d5db;
  padding-left: 1.5rem;
}

.instructions li {
  margin-bottom: 0.5rem;
}

.backButton {
  background: #6b7280;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.backButton:hover {
  background: #4b5563;
  transform: translateY(-1px);
}
