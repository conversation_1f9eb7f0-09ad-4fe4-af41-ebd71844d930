/* Overlay do modal */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10001;
  padding: 20px;
  overflow-y: auto;
}

/* Modal principal */
.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
  border: 2px solid #1db954;
  margin: 60px auto 60px auto;
  animation: slideIn 0.3s ease-out;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(29, 185, 84, 0.3);
}

.header h2 {
  color: #1db954;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
}

.closeButton {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 1.2rem;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Conteúdo */
.content {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

/* Informações da sala */
.roomInfo,
.createInfo {
  text-align: center;
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(29, 185, 84, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

.roomInfo h3,
.createInfo h3 {
  color: #1db954;
  margin: 0 0 15px;
  font-size: 1.1rem;
}

.roomCode {
  background: rgba(29, 185, 84, 0.2);
  padding: 10px 15px;
  border-radius: 10px;
  margin: 15px 0;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.roomCode span {
  color: #1db954;
  font-size: 1.2rem;
}

.roomInfo p,
.createInfo p {
  color: #b3b3b3;
  margin: 15px 0 0;
  line-height: 1.5;
}

/* Mensagem de sucesso */
.successMessage {
  text-align: center;
  padding: 30px 20px;
}

.successIcon {
  color: #22c55e;
  font-size: 3rem;
  margin-bottom: 20px;
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.successMessage h3 {
  color: #22c55e;
  margin: 0 0 15px;
  font-size: 1.2rem;
}

.successMessage p {
  color: #b3b3b3;
  margin: 10px 0;
  line-height: 1.5;
}

.successMessage ul {
  color: white;
  text-align: left;
  display: inline-block;
  margin: 15px 0;
  padding-left: 20px;
}

.successMessage li {
  margin-bottom: 5px;
  font-weight: 600;
}

/* Sem amigos */
.noFriends {
  text-align: center;
  padding: 40px 20px;
  color: #888;
}

.noFriendsIcon {
  font-size: 3rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.noFriends h4 {
  color: white;
  margin: 0 0 15px;
  font-size: 1.1rem;
}

.noFriends p {
  margin: 10px 0;
  line-height: 1.5;
}

/* Header da lista de amigos */
.friendsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.friendsHeader h4 {
  color: white;
  margin: 0;
  font-size: 1rem;
}

.selectAllButton {
  background: none;
  border: 1px solid #1db954;
  color: #1db954;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.8rem;
  font-weight: 600;
}

.selectAllButton:hover {
  background: #1db954;
  color: white;
}

/* Lista de amigos */
.friendsList {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.friendItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.friendItem:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(29, 185, 84, 0.5);
}

.friendItem.selected {
  background: rgba(29, 185, 84, 0.2);
  border-color: #1db954;
}

.friendInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.friendDetails {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.friendName {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.friendUsername {
  color: #888;
  font-size: 0.8rem;
}

.friendLevel {
  color: #1db954;
  font-size: 0.8rem;
  font-weight: 600;
}

.checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid #1db954;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1db954;
  font-size: 0.8rem;
}

.online::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  background: #22c55e;
  border: 2px solid white;
  border-radius: 50%;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-top: 1px solid rgba(29, 185, 84, 0.3);
}

.selectedCount {
  color: #888;
  font-size: 0.9rem;
}

.actions {
  display: flex;
  gap: 12px;
}

.cancelButton,
.inviteButton {
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancelButton {
  background: transparent;
  color: #888;
  border: 1px solid #888;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.inviteButton {
  background: #1db954;
  color: white;
}

.inviteButton:hover:not(:disabled) {
  background: #1ed760;
  transform: translateY(-2px);
}

.inviteButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding: 10px;
  }

  .modal {
    max-width: 100%;
    max-height: 95vh;
  }

  .content {
    padding: 20px;
  }

  .friendItem {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .footer {
    flex-direction: column;
    gap: 15px;
  }

  .actions {
    width: 100%;
    justify-content: center;
  }

  .cancelButton,
  .inviteButton {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .modal {
    border-radius: 15px;
  }

  .content {
    padding: 15px;
  }

  .header {
    padding: 15px 20px;
  }

  .header h2 {
    font-size: 1.1rem;
  }

  .footer {
    padding: 15px 20px;
  }

  .actions {
    flex-direction: column;
  }
}
