/* Estilos para componentes de <PERSON> */

.adContainer {
  position: relative;
  margin: 1rem 0;
  text-align: center;
  background: #1a1a1a;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #333;
}

.adLabel {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: #999;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 3px;
  z-index: 10;
}

.adPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #666;
  min-height: 100px;
}

.loadingSpinner {
  width: 30px;
  height: 30px;
  border: 3px solid #333;
  border-top: 3px solid #1DB954;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.adFallback {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border: 2px solid #1DB954;
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  margin: 1rem 0;
}

.fallbackContent p {
  color: #fff;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.donateButton {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.donateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* Posicionamentos específicos */
.headerAd {
  margin: 1rem auto;
  max-width: 728px;
  width: 100%;
  display: block;
  clear: both;
  position: relative;
  z-index: 1;
}

.sidebarAd {
  position: sticky;
  top: 20px;
  margin: 1rem 0;
  max-width: 300px;
}

.betweenGamesAd {
  margin: 2rem auto;
  max-width: 728px;
}

.footerAd {
  margin: 1rem auto 0 auto;
  max-width: 728px;
}

/* Anúncio intersticial */
.interstitialOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.interstitialContent {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  padding: 2rem;
  text-align: center;
  border: 2px solid #333;
}

.interstitialHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.interstitialHeader h3 {
  color: #fff;
  margin: 0;
  font-size: 1.5rem;
}

.skipButton {
  background: #666;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skipButton:hover {
  background: #777;
}

.countdown {
  color: #999;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.interstitialAd {
  margin: 1.5rem 0;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2a2a2a;
  border-radius: 10px;
}

.interstitialFooter {
  margin-top: 1.5rem;
}

.interstitialFooter p {
  color: #ccc;
  margin: 0 0 1rem 0;
}

/* Responsividade */
@media (max-width: 768px) {
  .headerAd {
    max-width: 100%;
    margin: 0.5rem auto;
    padding: 0 0.5rem;
  }

  .betweenGamesAd,
  .footerAd {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .sidebarAd {
    display: none; /* Ocultar sidebar em mobile */
  }

  .interstitialContent {
    margin: 1rem;
    padding: 1.5rem;
  }

  .headerAd {
    margin: 0.25rem auto;
    padding: 0 0.25rem;
  }

  .interstitialHeader h3 {
    font-size: 1.2rem;
  }

  .interstitialAd {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .adContainer {
    margin: 0.5rem 0;
  }

  .adFallback {
    padding: 1rem;
  }

  .fallbackContent p {
    font-size: 1rem;
  }

  .premiumButton {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .interstitialContent {
    padding: 1rem;
  }

  .interstitialAd {
    min-height: 200px;
  }
}
